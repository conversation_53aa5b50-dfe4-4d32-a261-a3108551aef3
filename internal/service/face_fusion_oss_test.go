package service

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/stretchr/testify/assert"
)

// TestFaceFusionResultUpdate 测试人脸融合结果更新和OSS上传
func TestFaceFusionResultUpdate(t *testing.T) {
	ctx := context.Background()
	faceFusionService := SingletonFaceFusionService()

	// 创建测试任务
	taskReq := &bean.FaceFusionTaskRequest{
		GameID:  "test-oss-game",
		UserID:  "test-oss-user",
		ModelID: "test-oss-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/test-face.jpg"},
		},
		TaskID:      "test-oss-task-001",
		SubmittedAt: time.Now(),
		Priority:    0,
	}

	// 创建记录
	record, err := faceFusionService.CreateFaceFusionRecord(ctx, taskReq)
	if err != nil {
		t.Logf("Create record failed (expected in test environment): %v", err)
		return
	}

	assert.NotNil(t, record)
	t.Logf("Created face fusion record: %d", record.ID)

	// 测试成功结果更新（包含base64图片）
	result := &bean.FaceFusionTaskResult{
		TaskID:      taskReq.TaskID,
		GameID:      taskReq.GameID,
		UserID:      taskReq.UserID,
		ModelID:     taskReq.ModelID,
		ProjectID:   "test-project-oss",
		FusedImage:  "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		RequestID:   "test-request-oss",
		Status:      "success",
		Message:     "Face fusion completed successfully with OSS upload",
		ProcessedAt: time.Now(),
	}

	err = faceFusionService.UpdateFaceFusionResult(ctx, taskReq.TaskID, result)
	if err != nil {
		t.Logf("Update result failed (expected in test environment): %v", err)
		return
	}

	t.Logf("Face fusion result updated successfully with OSS integration")

	// 验证result.FusedImage已被清空（避免内存占用）
	assert.Empty(t, result.FusedImage, "FusedImage should be cleared after upload")
}

// TestFaceFusionErrorHandling 测试人脸融合错误处理
func TestFaceFusionErrorHandling(t *testing.T) {
	ctx := context.Background()
	faceFusionService := SingletonFaceFusionService()

	// 测试失败结果处理
	result := &bean.FaceFusionTaskResult{
		TaskID:      "test-error-task",
		GameID:      "test-error-game",
		UserID:      "test-error-user",
		ModelID:     "test-error-model",
		ProjectID:   "test-project-error",
		FusedImage:  "some-base64-data",
		RequestID:   "test-request-error",
		Status:      "failed",
		Message:     "Face fusion failed due to invalid input",
		ProcessedAt: time.Now(),
	}

	err := faceFusionService.UpdateFaceFusionResult(ctx, "test-error-task", result)
	if err != nil {
		t.Logf("Update error result failed (expected in test environment): %v", err)
		return
	}

	t.Logf("Face fusion error result handled successfully")
}
