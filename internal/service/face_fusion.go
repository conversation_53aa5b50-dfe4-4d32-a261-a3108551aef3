package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_faceFusionOnce    sync.Once
	_faceFusionService *FaceFusionService
)

type FaceFusionService struct{}

func SingletonFaceFusionService() *FaceFusionService {
	_faceFusionOnce.Do(func() {
		_faceFusionService = &FaceFusionService{}
	})
	return _faceFusionService
}

// CreateFaceFusionRecord 创建人脸融合记录
func (s *FaceFusionService) CreateFaceFusionRecord(ctx context.Context, req *bean.FaceFusionTaskRequest) (*model.MFaceFusion, error) {
	logger.Logger.InfofCtx(ctx, "[人脸融合] 创建数据库记录: task_id=%s, game_id=%s, user_id=%s", req.TaskID, req.GameID, req.UserID)

	// 序列化MergeInfos为JSON
	mergeInfosJSON, err := json.Marshal(req.MergeInfos)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 序列化MergeInfos失败: %v", err)
		return nil, fmt.Errorf("marshal merge infos: %w", err)
	}

	// 转换参数类型
	var fuseFaceDegree int32
	if req.FuseFaceDegree != nil {
		fuseFaceDegree = int32(*req.FuseFaceDegree)
	}

	var fuseProfileDegree int32
	if req.FuseProfileDegree != nil {
		fuseProfileDegree = int32(*req.FuseProfileDegree)
	}

	var logoAdd int32
	if req.LogoAdd != nil {
		logoAdd = int32(*req.LogoAdd)
	}

	now := time.Now().UnixMilli()
	record := &model.MFaceFusion{
		GameID:            req.GameID,
		UserID:            req.UserID,
		TaskID:            req.TaskID,
		ModelID:           req.ModelID,
		ProjectID:         "", // 将在处理时设置
		RequestID:         "",
		ImageURL:          "",
		OssURL:            "",
		Status:            "processing",
		Message:           "Face fusion task submitted",
		MergeInfos:        string(mergeInfosJSON),
		FuseFaceDegree:    fuseFaceDegree,
		FuseProfileDegree: fuseProfileDegree,
		LogoAdd:           logoAdd,
		LogoParam:         req.LogoParam,
		FuseParam:         req.FuseParam,
		RspImgType:        req.RspImgType,
		CreatedAt:         now,
		UpdatedAt:         now,
		ProcessedAt:       0,
	}

	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	err = faceFusionCtx.Create(record)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 创建数据库记录失败: %v", err)
		return nil, fmt.Errorf("create face fusion record: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 创建数据库记录成功: id=%d, task_id=%s", record.ID, record.TaskID)
	return record, nil
}

// UpdateFaceFusionResult 更新人脸融合结果
func (s *FaceFusionService) UpdateFaceFusionResult(ctx context.Context, taskID string, result *bean.FaceFusionTaskResult) error {
	logger.Logger.InfofCtx(ctx, "[人脸融合] 更新处理结果: task_id=%s, status=%s", taskID, result.Status)

	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	now := time.Now().UnixMilli()
	updates := map[string]interface{}{
		"project_id":   result.ProjectID,
		"request_id":   result.RequestID,
		"status":       result.Status,
		"message":      result.Message,
		"updated_at":   now,
		"processed_at": now,
	}

	// 如果有融合后的图片，更新图片URL
	if result.FusedImage != "" {
		// 如果是URL格式，从URL下载并上传到OSS
		if result.Status == "success" {
			uploadService := SingletonUploadService()
			fileName := fmt.Sprintf("face_fusion_%s_%d.jpg", taskID, time.Now().Unix())

			logger.Logger.InfofCtx(ctx, "[人脸融合] 开始从URL上传融合图片到OSS: task_id=%s, 源URL=%s",
				taskID, result.FusedImage)

			// 使用独立的context进行图片上传，避免受上游超时影响
			// 设置10分钟的上传超时时间，足够完成图片下载和上传操作
			uploadCtx, uploadCancel := context.WithTimeout(context.Background(), 10*time.Minute)
			defer uploadCancel()

			// 复制必要的trace信息到新context中
			if traceID := ctx.Value(constants.TrackKey); traceID != nil {
				uploadCtx = context.WithValue(uploadCtx, constants.TrackKey, traceID)
			}

			// 使用带重试的图片上传
			imageURL, err := s.uploadImageWithRetry(uploadCtx, uploadService, result.FusedImage, fileName, taskID)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[人脸融合] 上传图片到OSS失败（包含重试）: task_id=%s, err=%v", taskID, err)
				// 上传失败时设置失败状态和错误信息
				updates["status"] = "failed"
				updates["message"] = fmt.Sprintf("Face fusion completed but image upload failed: %v", err)
				// 清空fusedImage，避免在数据库中存储原始URL
				result.FusedImage = ""
			} else {
				updates["image_url"] = imageURL
				updates["oss_url"] = imageURL                                                         // 预签名URL，提供安全访问
				logger.Logger.InfofCtx(ctx, "[人脸融合] 图片上传OSS成功: task_id=%s, url=%s", taskID, imageURL) // 注意：安全URL的生成现在在任务处理的回调前统一处理

				// 清空result.FusedImage，确保不在内存中保留原始URL
				result.FusedImage = ""
			}
		} else {
			// 非成功状态时不上传图片，但记录原始错误信息
			logger.Logger.InfofCtx(ctx, "[人脸融合] 任务非成功状态，跳过图片上传: task_id=%s, status=%s", taskID, result.Status)
		}
	}

	_, err := faceFusionCtx.Where(faceFusion.TaskID.Eq(taskID)).Updates(updates)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 更新数据库记录失败: %v", err)
		return fmt.Errorf("update face fusion record: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 更新处理结果成功: task_id=%s", taskID)
	return nil
}

// uploadImageWithRetry 带重试机制的图片上传
func (s *FaceFusionService) uploadImageWithRetry(ctx context.Context, uploadService *UploadService, sourceImageURL, fileName, taskID string) (string, error) {
	maxRetries := 3
	retryDelays := []time.Duration{
		1 * time.Second,
		3 * time.Second,
		5 * time.Second,
	}

	var lastErr error
	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			logger.Logger.InfofCtx(ctx, "[人脸融合] 图片上传重试 %d/%d: task_id=%s", attempt+1, maxRetries, taskID)

			// 检查context是否已取消
			select {
			case <-ctx.Done():
				return "", fmt.Errorf("upload context canceled during retry: %w", ctx.Err())
			case <-time.After(retryDelays[attempt-1]):
				// 继续重试
			}
		}

		// 使用预签名URL，设置8天有效期
		resultImageURL, err := uploadService.UploadImageFromURLAndGetPresignedURL(ctx, sourceImageURL, fileName, 192*time.Hour)
		if err != nil {
			lastErr = err

			// 判断是否为可重试的错误
			if s.isRetryableError(err) {
				logger.Logger.WarnfCtx(ctx, "[人脸融合] 图片上传失败，将重试: task_id=%s, attempt=%d, err=%v", taskID, attempt+1, err)
				continue
			} else {
				// 不可重试的错误，直接返回
				logger.Logger.ErrorfCtx(ctx, "[人脸融合] 图片上传遇到不可重试错误: task_id=%s, err=%v", taskID, err)
				return "", err
			}
		}

		// 上传成功
		if attempt > 0 {
			logger.Logger.InfofCtx(ctx, "[人脸融合] 图片上传重试成功: task_id=%s, attempt=%d", taskID, attempt+1)
		}
		return resultImageURL, nil
	}

	// 所有重试都失败了
	logger.Logger.ErrorfCtx(ctx, "[人脸融合] 图片上传重试全部失败: task_id=%s, attempts=%d, last_err=%v", taskID, maxRetries, lastErr)
	return "", fmt.Errorf("upload failed after %d attempts: %w", maxRetries, lastErr)
}

// isRetryableError 判断错误是否可重试
func (s *FaceFusionService) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// Context相关错误不重试
	if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
		return false
	}

	// 网络相关错误可重试
	if len(errStr) > 0 {
		retryableErrors := []string{
			"connection refused",
			"connection reset",
			"timeout",
			"temporary failure",
			"network is unreachable",
			"no such host",
			"connection timed out",
			"dial tcp",
			"EOF",
		}

		for _, retryableErr := range retryableErrors {
			if len(errStr) > len(retryableErr) && errStr[:len(retryableErr)] == retryableErr {
				return true
			}
		}
	}

	return true // 默认认为可以重试
}

// GetFaceFusionByTaskID 根据任务ID获取人脸融合记录
func (s *FaceFusionService) GetFaceFusionByTaskID(ctx context.Context, taskID string) (*model.MFaceFusion, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	record, err := faceFusionCtx.Where(faceFusion.TaskID.Eq(taskID)).First()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 查询记录失败: task_id=%s, err=%v", taskID, err)
		return nil, fmt.Errorf("get face fusion record: %w", err)
	}

	return record, nil
}

// GetFaceFusionByGameIDAndUserID 根据游戏ID和用户ID获取人脸融合记录列表
func (s *FaceFusionService) GetFaceFusionByGameIDAndUserID(ctx context.Context, gameID, userID string, limit int) ([]*model.MFaceFusion, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	query := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.UserID.Eq(userID)).
		Order(faceFusion.CreatedAt.Desc())

	if limit > 0 {
		query = query.Limit(limit)
	}

	records, err := query.Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 查询用户记录失败: game_id=%s, user_id=%s, err=%v", gameID, userID, err)
		return nil, fmt.Errorf("get user face fusion records: %w", err)
	}

	return records, nil
}

// GetFaceFusionByStatus 根据状态获取人脸融合记录列表
func (s *FaceFusionService) GetFaceFusionByStatus(ctx context.Context, status string, limit int) ([]*model.MFaceFusion, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	query := faceFusionCtx.Where(faceFusion.Status.Eq(status)).
		Order(faceFusion.CreatedAt.Desc())

	if limit > 0 {
		query = query.Limit(limit)
	}

	records, err := query.Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 查询状态记录失败: status=%s, err=%v", status, err)
		return nil, fmt.Errorf("get face fusion records by status: %w", err)
	}

	return records, nil
}

// GetFaceFusionStats 获取人脸融合统计信息
func (s *FaceFusionService) GetFaceFusionStats(ctx context.Context, gameID string) (map[string]int64, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	stats := make(map[string]int64)

	// 总数
	total, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID)).Count()
	if err != nil {
		return nil, fmt.Errorf("count total records: %w", err)
	}
	stats["total"] = total

	// 成功数
	success, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.Status.Eq("success")).Count()
	if err != nil {
		return nil, fmt.Errorf("count success records: %w", err)
	}
	stats["success"] = success

	// 失败数
	failed, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.Status.Eq("failed")).Count()
	if err != nil {
		return nil, fmt.Errorf("count failed records: %w", err)
	}
	stats["failed"] = failed

	// 处理中数
	processing, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.Status.Eq("processing")).Count()
	if err != nil {
		return nil, fmt.Errorf("count processing records: %w", err)
	}
	stats["processing"] = processing

	return stats, nil
}
