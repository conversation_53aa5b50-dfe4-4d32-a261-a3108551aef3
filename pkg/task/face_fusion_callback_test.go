package task

import (
	"encoding/json"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
)

// TestFaceFusionCallbackTaskStructure 测试人脸融合回调任务结构
func TestFaceFusionCallbackTaskStructure(t *testing.T) {
	// 创建测试回调任务
	result := &bean.FaceFusionTaskResult{
		TaskID:      "test-task-123",
		GameID:      "test-game",
		UserID:      "test-user",
		ModelID:     "test-model",
		ProjectID:   "test-project",
		FusedImage:  "https://oss.example.com/fused-image.jpg",
		RequestID:   "req-123",
		Status:      "success",
		Message:     "Face fusion completed successfully",
		ProcessedAt: time.Now(),
	}

	callbackTask := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result:      result,
		ScheduledAt: time.Now(),
	}

	// 测试序列化和反序列化
	data, err := json.Marshal(callbackTask)
	assert.NoError(t, err)

	var parsedTask bean.FaceFusionCallbackTask
	err = json.Unmarshal(data, &parsedTask)
	assert.NoError(t, err)

	// 验证数据完整性
	assert.Equal(t, callbackTask.Attempt, parsedTask.Attempt)
	assert.Equal(t, callbackTask.GameID, parsedTask.GameID)
	assert.Equal(t, callbackTask.CallbackURL, parsedTask.CallbackURL)
	assert.Equal(t, callbackTask.Result.TaskID, parsedTask.Result.TaskID)
	assert.Equal(t, callbackTask.Result.Status, parsedTask.Result.Status)
}

// TestValidateFaceFusionCallbackTask 测试回调任务验证
func TestValidateFaceFusionCallbackTask(t *testing.T) {
	// 测试有效的回调任务
	validTask := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result: &bean.FaceFusionTaskResult{
			TaskID: "test-task-123",
			Status: "success",
		},
		ScheduledAt: time.Now(),
	}

	err := validateFaceFusionCallbackTask(validTask)
	assert.NoError(t, err)

	// 测试缺少Result的情况
	invalidTask1 := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result:      nil,
		ScheduledAt: time.Now(),
	}

	err = validateFaceFusionCallbackTask(invalidTask1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "face fusion callback result is nil")

	// 测试缺少GameID的情况
	invalidTask2 := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result: &bean.FaceFusionTaskResult{
			TaskID: "test-task-123",
		},
		ScheduledAt: time.Now(),
	}

	err = validateFaceFusionCallbackTask(invalidTask2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "game_id is required")

	// 测试缺少CallbackURL的情况
	invalidTask3 := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "",
		Result: &bean.FaceFusionTaskResult{
			TaskID: "test-task-123",
		},
		ScheduledAt: time.Now(),
	}

	err = validateFaceFusionCallbackTask(invalidTask3)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "callback_url is required")

	// 测试缺少TaskID的情况
	invalidTask4 := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result: &bean.FaceFusionTaskResult{
			TaskID: "",
		},
		ScheduledAt: time.Now(),
	}

	err = validateFaceFusionCallbackTask(invalidTask4)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "task_id is required")
}

// TestFaceFusionCallbackTaskUnmarshaling 测试回调任务解析
func TestFaceFusionCallbackTaskUnmarshaling(t *testing.T) {
	// 创建测试回调任务
	callbackTask := &bean.FaceFusionCallbackTask{
		Attempt:     2,
		GameID:      "test-game",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result: &bean.FaceFusionTaskResult{
			TaskID:     "test-task-123",
			GameID:     "test-game",
			UserID:     "test-user",
			Status:     "success",
			FusedImage: "https://oss.example.com/fused-image.jpg",
		},
		ScheduledAt: time.Now(),
	}

	// 序列化为asynq任务
	payload, err := json.Marshal(callbackTask)
	assert.NoError(t, err)

	task := asynq.NewTask(TypeFaceFusionCallback, payload)
	assert.Equal(t, TypeFaceFusionCallback, task.Type())

	// 测试解析（模拟HandleFaceFusionCallbackTask中的解析逻辑）
	var parsedTask bean.FaceFusionCallbackTask
	err = json.Unmarshal(task.Payload(), &parsedTask)
	assert.NoError(t, err)

	assert.Equal(t, callbackTask.Attempt, parsedTask.Attempt)
	assert.Equal(t, callbackTask.GameID, parsedTask.GameID)
	assert.Equal(t, callbackTask.CallbackURL, parsedTask.CallbackURL)
	assert.Equal(t, callbackTask.Result.TaskID, parsedTask.Result.TaskID)
	assert.Equal(t, callbackTask.Result.Status, parsedTask.Result.Status)
	assert.Equal(t, callbackTask.Result.FusedImage, parsedTask.Result.FusedImage)

	t.Logf("Face fusion callback task unmarshaling test passed")
}

// TestFaceFusionCallbackRetryLogic 测试回调重试逻辑
func TestFaceFusionCallbackRetryLogic(t *testing.T) {
	// 创建测试回调任务
	callbackTask := &bean.FaceFusionCallbackTask{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "https://callback.example.com/face_fusion",
		Result: &bean.FaceFusionTaskResult{
			TaskID: "test-task-123",
			Status: "success",
		},
		ScheduledAt: time.Now(),
	}

	// 序列化任务
	payload, err := json.Marshal(callbackTask)
	assert.NoError(t, err)

	task := asynq.NewTask(TypeFaceFusionCallback, payload)

	// 注意：这里我们不能直接调用HandleFaceFusionCallbackTask，
	// 因为它需要实际的HTTP回调和数据库连接
	// 但我们可以测试任务的基本结构和验证逻辑

	// 测试任务解析
	var parsedTask bean.FaceFusionCallbackTask
	err = json.Unmarshal(task.Payload(), &parsedTask)
	assert.NoError(t, err)

	// 测试验证逻辑
	err = validateFaceFusionCallbackTask(&parsedTask)
	assert.NoError(t, err)

	t.Logf("Face fusion callback retry logic test passed")
}
