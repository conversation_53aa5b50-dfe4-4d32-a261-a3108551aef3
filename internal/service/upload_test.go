package service

import (
	"testing"
)

// TestGetPresignedUploadURL 测试获取预签名上传URL
func TestGetPresignedUploadURL(t *testing.T) {
	// service := SingletonUploadService()
	// ctx := context.Background()

	// // 测试生成预签名URL
	// fileName := "/test/example.jpg"
	// expiration := time.Hour

	// presignedURL, err := service.GetPresignedUploadURL(ctx, fileName, expiration)
	// if err != nil {
	// 	t.Fatalf("生成预签名URL失败: %v", err)
	// }

	// // 验证返回的URL格式
	// parsedURL, err := url.Parse(presignedURL)
	// if err != nil {
	// 	t.Fatalf("预签名URL格式无效: %v", err)
	// }

	// // 检查URL是否包含必要的查询参数
	// query := parsedURL.Query()
	// if query.Get("q-sign-algorithm") == "" {
	// 	t.Error("预签名URL缺少签名算法参数")
	// }
	// if query.Get("q-signature") == "" {
	// 	t.Error("预签名URL缺少签名参数")
	// }

	// t.Logf("生成的预签名URL: %s", presignedURL)
}

// TestGetPresignedUploadURLWithOptions 测试带选项的预签名上传URL
func TestGetPresignedUploadURLWithOptions(t *testing.T) {
	// service := SingletonUploadService()
	// ctx := context.Background()

	// // 创建带有特定请求头的选项
	// options := &cos.PresignedURLOptions{
	// 	Query:  &url.Values{},
	// 	Header: &http.Header{},
	// }

	// // 设置Content-Type头部
	// options.Header.Set("Content-Type", "image/jpeg")

	// fileName := "/test/example-with-options.jpg"
	// expiration := time.Hour

	// presignedURL, err := service.GetPresignedUploadURLWithOptions(ctx, fileName, expiration, options)
	// if err != nil {
	// 	t.Fatalf("生成带选项的预签名URL失败: %v", err)
	// }

	// // 验证返回的URL格式
	// _, err = url.Parse(presignedURL)
	// if err != nil {
	// 	t.Fatalf("预签名URL格式无效: %v", err)
	// }

	// t.Logf("生成的带选项预签名URL: %s", presignedURL)
}

// ExampleUploadService_GetPresignedUploadURL 展示如何使用预签名URL进行上传的示例
func ExampleUploadService_GetPresignedUploadURL() {
	// 这是一个演示用户如何使用预签名URL的示例
	// 实际使用时，用户会从服务获取预签名URL，然后使用如下代码上传文件

	// presignedURL := "从服务获取的预签名URL"
	// fileData := []byte("文件内容")
	//
	// req, err := http.NewRequest(http.MethodPut, presignedURL, bytes.NewReader(fileData))
	// if err != nil {
	//     // 处理错误
	// }
	//
	// // 用户可自行设置请求头部
	// req.Header.Set("Content-Type", "image/jpeg")
	// req.Header.Set("Cache-Control", "max-age=3600")
	//
	// client := &http.Client{Timeout: 30 * time.Second}
	// resp, err := client.Do(req)
	// if err != nil {
	//     // 处理错误
	// }
	// defer resp.Body.Close()
	//
	// if resp.StatusCode == http.StatusOK {
	//     // 上传成功
	// }
}
