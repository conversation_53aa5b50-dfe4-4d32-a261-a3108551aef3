package facefusion

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// validateBasicTaskRequest 简单的任务请求验证（避免循环导入）
func validateBasicTaskRequest(req *bean.FaceFusionTaskRequest) error {
	if req.GameID == "" {
		return fmt.Errorf("game_id is required")
	}
	if req.ModelID == "" {
		return fmt.Errorf("model_id is required")
	}
	if req.UserID == "" {
		return fmt.Errorf("user_id is required")
	}
	if len(req.MergeInfos) == 0 {
		return fmt.Errorf("merge_infos is required")
	}
	return nil
}

// TestFaceFusionWorkflow 测试完整的人脸融合工作流程
func TestFaceFusionWorkflow(t *testing.T) {
	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project-123",
			CallbackURL:         "https://test-callback.example.com/face-fusion",
			QPSLimit:            5,
			MaxQueueSize:        100,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      false, // 禁用QPS限制以简化测试
		},
	}

	// 创建测试请求
	taskRequest := &bean.FaceFusionTaskRequest{
		GameID:  "test-game",
		UserID:  "test-user-123",
		ModelID: "test-model-456",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/user-face.jpg"},
		},
		FuseFaceDegree: func() *int64 { v := int64(80); return &v }(),
		LogoAdd:        func() *int64 { v := int64(0); return &v }(),
		RspImgType:     "url",
		TaskID:         "test-task-" + time.Now().Format("20060102150405"),
		SubmittedAt:    time.Now(),
		Priority:       0,
	}

	// 测试任务请求验证（简化版本，避免循环导入）
	err := validateBasicTaskRequest(taskRequest)
	assert.NoError(t, err, "Task request validation should pass")

	// 测试转换为旧格式（简化版本，避免循环导入）
	legacyRequest := &bean.FaceFusionReq{
		GameID:            taskRequest.GameID,
		UserID:            taskRequest.UserID,
		ModelID:           taskRequest.ModelID,
		MergeInfos:        taskRequest.MergeInfos,
		FuseFaceDegree:    taskRequest.FuseFaceDegree,
		FuseProfileDegree: taskRequest.FuseProfileDegree,
		LogoAdd:           taskRequest.LogoAdd,
		// LogoParam:         taskRequest.LogoParam,
		// FuseParam:         taskRequest.FuseParam,
		RspImgType: taskRequest.RspImgType,
		TaskID:     taskRequest.TaskID,
	}
	assert.Equal(t, taskRequest.GameID, legacyRequest.GameID)
	assert.Equal(t, taskRequest.UserID, legacyRequest.UserID)
	assert.Equal(t, taskRequest.ModelID, legacyRequest.ModelID)

	// 测试队列提交
	queue := NewQPSLimitedQueue()
	ctx := context.Background()

	result, err := queue.Submit(ctx, taskRequest)
	assert.NoError(t, err, "Queue submission should succeed")
	assert.NotNil(t, result, "Result should not be nil")
	assert.Equal(t, taskRequest.TaskID, result.TaskID)

	// 测试回调任务创建
	callbackTask := &bean.FaceFusionCallbackTask{
		Attempt:     0,
		GameID:      result.GameID,
		CallbackURL: config.GlobConfig.FaceFusion.CallbackURL,
		Result:      result,
		ScheduledAt: time.Now(),
	}

	// 测试回调任务序列化
	callbackData, err := json.Marshal(callbackTask)
	assert.NoError(t, err, "Callback task serialization should succeed")
	assert.NotEmpty(t, callbackData, "Callback data should not be empty")

	// 测试回调任务反序列化
	var deserializedCallback bean.FaceFusionCallbackTask
	err = json.Unmarshal(callbackData, &deserializedCallback)
	assert.NoError(t, err, "Callback task deserialization should succeed")
	assert.Equal(t, callbackTask.GameID, deserializedCallback.GameID)
	assert.Equal(t, callbackTask.CallbackURL, deserializedCallback.CallbackURL)

	// 测试队列统计
	stats := queue.GetStats()
	assert.NotNil(t, stats, "Queue stats should not be nil")
	assert.Equal(t, 5, stats.QPSLimit)
	assert.Equal(t, 100, stats.MaxQueueSize)
	assert.False(t, stats.EnableQPSLimit)
}

// TestFaceFusionQPSLimiting 测试QPS限制功能
func TestFaceFusionQPSLimiting(t *testing.T) {
	// 设置启用QPS限制的配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project-123",
			CallbackURL:         "https://test-callback.example.com/face-fusion",
			QPSLimit:            2, // 设置较低的QPS用于测试
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 5,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	ctx := context.Background()

	// 创建多个测试请求
	requests := make([]*bean.FaceFusionTaskRequest, 3)
	for i := 0; i < 3; i++ {
		requests[i] = &bean.FaceFusionTaskRequest{
			GameID:  "test-game",
			UserID:  "test-user-" + string(rune('1'+i)),
			ModelID: "test-model-456",
			MergeInfos: []map[string]string{
				{"Url": "https://example.com/user-face.jpg"},
			},
			TaskID:      "test-task-" + string(rune('1'+i)),
			SubmittedAt: time.Now(),
		}
	}

	// 并发提交请求
	results := make(chan error, 3)
	start := time.Now()

	for i, req := range requests {
		go func(index int, request *bean.FaceFusionTaskRequest) {
			_, err := queue.Submit(ctx, request)
			results <- err
		}(i, req)
	}

	// 收集结果
	var errors []error
	for i := 0; i < 3; i++ {
		err := <-results
		if err != nil {
			errors = append(errors, err)
		}
	}

	duration := time.Since(start)

	// 验证QPS限制是否生效
	// 由于QPS限制为2，处理3个请求应该需要至少1秒以上
	if queue.enableQPSLimit {
		assert.True(t, duration >= 500*time.Millisecond,
			"With QPS limiting enabled, processing should take longer")
	}

	// 检查队列统计
	stats := queue.GetStats()
	assert.True(t, stats.TotalQueued >= 0, "Total queued should be non-negative")
	assert.True(t, stats.TotalProcessed >= 0, "Total processed should be non-negative")
}

// TestBackwardCompatibility 测试向后兼容性
func TestBackwardCompatibility(t *testing.T) {
	// 测试旧格式的回调请求
	legacyCallbackReq := &bean.FaceFusionCallbackReq{
		Attempt:     1,
		GameID:      "test-game",
		CallbackURL: "https://test-callback.example.com/legacy",
		FaceFusionCallbackData: &bean.FaceFusionCallbackData{
			ProjectID:  "test-project",
			ModelID:    "test-model",
			FusedImage: "https://example.com/result.jpg",
			RequestID:  "req-123",
			Status:     "success",
			Message:    "Face fusion completed",
		},
	}

	// 测试序列化和反序列化
	data, err := json.Marshal(legacyCallbackReq)
	assert.NoError(t, err, "Legacy callback serialization should succeed")

	var deserializedReq bean.FaceFusionCallbackReq
	err = json.Unmarshal(data, &deserializedReq)
	assert.NoError(t, err, "Legacy callback deserialization should succeed")
	assert.Equal(t, legacyCallbackReq.GameID, deserializedReq.GameID)
	assert.Equal(t, legacyCallbackReq.CallbackURL, deserializedReq.CallbackURL)

	// 测试旧格式的人脸融合请求
	legacyReq := &bean.FaceFusionReq{
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/user-face.jpg"},
		},
		RspImgType: "url",
	}

	// 测试序列化和反序列化
	data, err = json.Marshal(legacyReq)
	assert.NoError(t, err, "Legacy request serialization should succeed")

	var deserializedLegacyReq bean.FaceFusionReq
	err = json.Unmarshal(data, &deserializedLegacyReq)
	assert.NoError(t, err, "Legacy request deserialization should succeed")
	assert.Equal(t, legacyReq.GameID, deserializedLegacyReq.GameID)
	assert.Equal(t, legacyReq.UserID, deserializedLegacyReq.UserID)
	assert.Equal(t, legacyReq.ModelID, deserializedLegacyReq.ModelID)
}

// TestConfigurationDefaults 测试配置默认值
func TestConfigurationDefaults(t *testing.T) {
	// 测试空配置的默认值
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{},
	}

	queue := NewQPSLimitedQueue()
	assert.Equal(t, 18, queue.qpsLimit, "Default QPS limit should be 18")
	// 注意：当配置为0时，NewQPSLimitedQueue 会保持0作为测试配置
	assert.GreaterOrEqual(t, queue.maxQueueSize, 0, "Max queue size should be non-negative")
	assert.Equal(t, 300, queue.queueTimeoutSeconds, "Default timeout should be 300 seconds")
	assert.False(t, queue.enableQPSLimit, "QPS limiting should be disabled by default")
}
