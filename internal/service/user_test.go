package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

func Init() {
	mustInit()
	initLogger()
	initMysql()
	initCron()
	initRedis()
}

func mustInit() {
	config.MustInit()
}

func initLogger() {
	logger.InitLogger(&config.GlobConfig.Logger)
}

func initMysql() {
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
}

func initCron() {
	//cron.InitCron()
}

func initRedis() {
	redis.InitRedis(&config.GlobConfig.Redis)
}

// 测试函数
func TestUserService_GetUserInfoByOpenID(t *testing.T) {
	Init()
	userMinigame, err := SingletonUserService().GetUserInfoByOpenID(context.Background(),
		"", "mygame", "", "1111", "22221111", "33311111", "44411111")
	if err != nil {
		return
	}
	logger.Logger.Debug("userMinigame :")
	logger.Logger.Debug(userMinigame)
}

func TestUserService_GetCode2Session(t *testing.T) {
	Init()
	res, err := SingletonMinigameService().GetCode2Session(context.Background(), "", "", "0b1rtjGa18NDhH0KQvJa1Q4V5N3rtjG9")
	if err != nil {
		return
	}
	logger.Logger.Debug("res :")
	logger.Logger.Debug(res)
}

// TestUserService_ValidateUserExists 测试用户验证功能
func TestUserService_ValidateUserExists(t *testing.T) {
	Init()
	s := SingletonUserService()
	ctx := context.Background()

	tests := []struct {
		name    string
		gameID  string
		userID  string
		wantErr bool
		errType error
	}{
		{
			name:    "空游戏ID",
			gameID:  "",
			userID:  "test-user-id",
			wantErr: true,
			errType: constants.ErrUserNotExists,
		},
		{
			name:    "空用户ID",
			gameID:  "test-game-id",
			userID:  "",
			wantErr: true,
			errType: constants.ErrUserNotExists,
		},
		{
			name:    "不存在的用户",
			gameID:  "nonexistent-game",
			userID:  "nonexistent-user",
			wantErr: true,
			errType: constants.ErrUserNotExists,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := s.ValidateUserExists(ctx, tt.gameID, tt.userID)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType, err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
