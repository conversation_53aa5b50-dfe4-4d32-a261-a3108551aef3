package facefusion

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestThreadSafetyFix 验证SubmitWithProcessor的线程安全修复
func TestThreadSafetyFix(t *testing.T) {
	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project",
			QPSLimit:            10,
			MaxQueueSize:        100,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	defer queue.Shutdown()

	ctx := context.Background()

	// 创建多个不同的处理器
	processor1 := func(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
		return &bean.FaceFusionTaskResult{
			TaskID: request.TaskID,
			Status: "processor1",
		}, nil
	}

	processor2 := func(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
		return &bean.FaceFusionTaskResult{
			TaskID: request.TaskID,
			Status: "processor2",
		}, nil
	}

	// 并发提交使用不同处理器的任务
	var wg sync.WaitGroup
	results := make(chan *bean.FaceFusionTaskResult, 20)

	for i := 0; i < 10; i++ {
		wg.Add(2)

		// 使用processor1
		go func(id int) {
			defer wg.Done()
			req := &bean.FaceFusionTaskRequest{
				TaskID:  fmt.Sprintf("task1-%d", id),
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image.jpg"},
				},
				SubmittedAt: time.Now(),
			}
			result, err := queue.SubmitWithProcessor(ctx, req, processor1)
			if err == nil && result != nil {
				results <- result
			}
		}(i)

		// 使用processor2
		go func(id int) {
			defer wg.Done()
			req := &bean.FaceFusionTaskRequest{
				TaskID:  fmt.Sprintf("task2-%d", id),
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image.jpg"},
				},
				SubmittedAt: time.Now(),
			}
			result, err := queue.SubmitWithProcessor(ctx, req, processor2)
			if err == nil && result != nil {
				results <- result
			}
		}(i)
	}

	wg.Wait()
	close(results)

	// 验证每个任务都使用了正确的处理器
	processor1Count := 0
	processor2Count := 0

	for result := range results {
		if result.Status == "processor1" {
			processor1Count++
			assert.Contains(t, result.TaskID, "task1-", "Task with processor1 should have correct TaskID prefix")
		} else if result.Status == "processor2" {
			processor2Count++
			assert.Contains(t, result.TaskID, "task2-", "Task with processor2 should have correct TaskID prefix")
		}
	}

	// 验证没有处理器混用
	assert.Equal(t, 10, processor1Count, "Should have 10 tasks processed by processor1")
	assert.Equal(t, 10, processor2Count, "Should have 10 tasks processed by processor2")
}

// TestQPSControlAccuracy 验证QPS控制的准确性
func TestQPSControlAccuracy(t *testing.T) {
	// 设置低QPS限制便于测试
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project",
			QPSLimit:            2, // 2 QPS
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	defer queue.Shutdown()

	ctx := context.Background()

	// 提交5个任务
	start := time.Now()
	var wg sync.WaitGroup

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			req := &bean.FaceFusionTaskRequest{
				TaskID:  fmt.Sprintf("qps-test-%d", id),
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image.jpg"},
				},
				SubmittedAt: time.Now(),
			}
			_, _ = queue.Submit(ctx, req)
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	// 5个任务，2 QPS，应该需要至少2秒
	assert.True(t, duration >= 2*time.Second,
		"Processing 5 tasks at 2 QPS should take at least 2 seconds, took: %v", duration)
}

// TestRestoredTaskHandling 验证恢复任务的处理
func TestRestoredTaskHandling(t *testing.T) {
	// 这个测试验证恢复的任务不会导致goroutine泄漏
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project",
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	defer queue.Shutdown()

	// 模拟恢复的任务（没有ResultChan）
	restoredTask := &QueuedTask{
		Request: &bean.FaceFusionTaskRequest{
			TaskID:  "restored-task",
			GameID:  "test-game",
			UserID:  "test-user",
			ModelID: "test-model",
			MergeInfos: []map[string]string{
				{"Url": "https://example.com/image.jpg"},
			},
			SubmittedAt: time.Now(),
		},
		SubmittedAt: time.Now(),
		ResultChan:  nil, // 恢复的任务没有结果通道
		Context:     context.Background(),
		Processor:   nil,
	}

	// 直接调用handleQueuedTask来测试
	// 这应该不会panic或导致goroutine泄漏
	assert.NotPanics(t, func() {
		queue.handleQueuedTask(restoredTask)
	}, "Handling restored task should not panic")
}
