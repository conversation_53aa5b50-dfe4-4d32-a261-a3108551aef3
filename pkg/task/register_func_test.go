package task

import (
	"encoding/json"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
)

// TestProductShipmentCompatibility 测试产品发货逻辑的兼容性
func TestProductShipmentCompatibility(t *testing.T) {
	// 这个测试确保直接调用和异步调用使用相同的核心逻辑

	// 模拟订单请求数据
	orderReq := &bean.OrderReq{
		Attempt: 0,
		Order: &bean.ProductShipmentOrder{
			OrderID: "test-order-123",
			GameID:  "test-game",
			UserID:  "test-user",
		},
		CallbackURL:   "https://example.com/callback",
		PlatformAppID: "test-app-id",
	}

	// 测试数据序列化兼容性
	orderByte, err := json.Marshal(orderReq)
	assert.NoError(t, err)

	// 测试反序列化兼容性
	var deserializedReq bean.OrderReq
	err = json.Unmarshal(orderByte, &deserializedReq)
	assert.NoError(t, err)

	// 验证数据结构完整性
	assert.Equal(t, orderReq.Order.OrderID, deserializedReq.Order.OrderID)
	assert.Equal(t, orderReq.Order.GameID, deserializedReq.Order.GameID)
	assert.Equal(t, orderReq.Order.UserID, deserializedReq.Order.UserID)
	assert.Equal(t, orderReq.CallbackURL, deserializedReq.CallbackURL)
	assert.Equal(t, orderReq.PlatformAppID, deserializedReq.PlatformAppID)
}

// TestDouyinPayTaskStructure 测试抖音支付任务数据结构
func TestDouyinPayTaskStructure(t *testing.T) {
	// 模拟抖音支付任务数据
	douyinReq := bean.DouyinPayTaskReq{
		AccessToken:    "test-token",
		OpenID:         "test-openid",
		PaySecret:      "test-secret",
		AppID:          "test-appid",
		OrderID:        "test-order-123",
		OrderPlatform:  "android",
		SaveAmt:        1000,
		DeductAmt:      100,
		IsSmallDiamond: false, // 测试非小额钻石支付
	}

	// 测试序列化
	reqByte, err := json.Marshal(douyinReq)
	assert.NoError(t, err)

	// 测试反序列化
	var deserializedReq bean.DouyinPayTaskReq
	err = json.Unmarshal(reqByte, &deserializedReq)
	assert.NoError(t, err)

	// 验证数据完整性
	assert.Equal(t, douyinReq.OrderID, deserializedReq.OrderID)
	assert.Equal(t, douyinReq.OpenID, deserializedReq.OpenID)
	assert.Equal(t, douyinReq.AppID, deserializedReq.AppID)
}

// TestTaskTypeConstants 测试任务类型常量
func TestTaskTypeConstants(t *testing.T) {
	// 确保任务类型常量正确定义
	assert.Equal(t, "douyin_pay_callback", TypeDouyinPayCallback)
	assert.Equal(t, "product_shipment_order", TypeProductShipmentOrder)
	assert.Equal(t, "face_fusion", TypeFaceFusion)
	assert.Equal(t, "face_fusion_callback", TypeFaceFusionCallback)

	// 测试任务创建
	douyinTask := asynq.NewTask(TypeDouyinPayCallback, []byte("test-payload"))
	assert.Equal(t, TypeDouyinPayCallback, douyinTask.Type())

	shipmentTask := asynq.NewTask(TypeProductShipmentOrder, []byte("test-payload"))
	assert.Equal(t, TypeProductShipmentOrder, shipmentTask.Type())

	faceFusionTask := asynq.NewTask(TypeFaceFusion, []byte("test-payload"))
	assert.Equal(t, TypeFaceFusion, faceFusionTask.Type())

	faceFusionCallbackTask := asynq.NewTask(TypeFaceFusionCallback, []byte("test-payload"))
	assert.Equal(t, TypeFaceFusionCallback, faceFusionCallbackTask.Type())
}

// TestTaskHandlerRegistration 测试任务处理器注册
func TestTaskHandlerRegistration(t *testing.T) {
	handlers := GetHandleTasks()

	// 确保所有任务类型都有对应的处理器
	assert.Contains(t, handlers, TypeFaceFusion, "FaceFusion task handler should be registered")
	assert.Contains(t, handlers, TypeFaceFusionCallback, "FaceFusionCallback task handler should be registered")
	assert.Contains(t, handlers, TypeReportCallback, "ReportCallback task handler should be registered")
	assert.Contains(t, handlers, TypeMonitorCallback, "MonitorCallback task handler should be registered")
	assert.Contains(t, handlers, TypeDouyinGiftDelivery, "DouyinGiftDelivery task handler should be registered")

	// 确保处理器不为nil
	assert.NotNil(t, handlers[TypeFaceFusion], "FaceFusion handler should not be nil")
	assert.NotNil(t, handlers[TypeFaceFusionCallback], "FaceFusionCallback handler should not be nil")
}

// BenchmarkProductShipmentSerialization 性能测试：数据序列化
func BenchmarkProductShipmentSerialization(b *testing.B) {
	orderReq := &bean.OrderReq{
		Attempt: 0,
		Order: &bean.ProductShipmentOrder{
			OrderID: "benchmark-order-123",
			GameID:  "benchmark-game",
			UserID:  "benchmark-user",
		},
		CallbackURL:   "https://example.com/callback",
		PlatformAppID: "benchmark-app-id",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(orderReq)
		if err != nil {
			b.Fatal(err)
		}
	}
}
