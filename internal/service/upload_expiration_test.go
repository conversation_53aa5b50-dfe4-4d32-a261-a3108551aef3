package service

import (
	"context"
	"testing"
	"time"
)

// TestPresignedURLExpirationDurations 测试不同有效期时长的有效性
func TestPresignedURLExpirationDurations(t *testing.T) {
	// 测试不同时长的Duration是否可以正确计算
	testCases := []struct {
		name       string
		expiration time.Duration
		hours      float64
	}{
		{
			name:       "24小时有效期",
			expiration: 24 * time.Hour,
			hours:      24,
		},
		{
			name:       "7天有效期",
			expiration: 7 * 24 * time.Hour,
			hours:      168, // 7 * 24
		},
		{
			name:       "8天有效期",
			expiration: 8 * 24 * time.Hour,
			hours:      192, // 8 * 24
		},
		{
			name:       "30天有效期",
			expiration: 30 * 24 * time.Hour,
			hours:      720, // 30 * 24
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actualHours := tc.expiration.Hours()
			if actualHours != tc.hours {
				t.Errorf("期望%f小时，实际得到%f小时", tc.hours, actualHours)
			} else {
				t.Logf("%s: %v = %f小时", tc.name, tc.expiration, actualHours)
			}
		})
	}
}

// TestPresignedURLDownloadLongExpiration 测试长时间有效期的预签名下载URL
func TestPresignedURLDownloadLongExpiration(t *testing.T) {
	service := SingletonUploadService()
	ctx := context.Background()

	// 测试8天有效期的下载URL
	fileName := "/face-fusion/test-file.jpg"
	expiration := 8 * 24 * time.Hour // 8天

	presignedURL, err := service.GetPresignedDownloadURL(ctx, fileName, expiration)
	if err != nil {
		t.Errorf("生成8天有效期下载URL失败: %v", err)
	} else {
		t.Logf("成功生成8天有效期下载URL: %s", presignedURL)
	}
}