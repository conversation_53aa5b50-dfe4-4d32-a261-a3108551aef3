package service

import (
	"context"
	"fmt"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	image "github.com/yidun/yidun-golang-sdk/yidun/service/antispam/image/v5"
	"github.com/yidun/yidun-golang-sdk/yidun/service/antispam/image/v5/check"
	imagesync "github.com/yidun/yidun-golang-sdk/yidun/service/antispam/image/v5/check/sync"
)

var (
	_imageDetectionService *ImageDetectionService
	_imageDetectionOnce    sync.Once
)

// ImageDetectionService 图片检测服务
type ImageDetectionService struct {
	client *image.ImageClient
}

// SingletonImageDetectionService 获取图片检测服务单例
func SingletonImageDetectionService() *ImageDetectionService {
	_imageDetectionOnce.Do(func() {
		_imageDetectionService = &ImageDetectionService{
			client: image.NewImageClientWithAccessKey(
				config.GlobConfig.NetEaseYidun.SecretID,
				config.GlobConfig.NetEaseYidun.SecretKey,
			),
		}
	})
	return _imageDetectionService
}

// DetectImagesInMergeInfos 检测MergeInfos中的图片
func (s *ImageDetectionService) DetectImagesInMergeInfos(ctx context.Context, mergeInfos []map[string]string) error {
	if s == nil {
		return fmt.Errorf("image detection service is nil")
	}
	if s.client == nil {
		return fmt.Errorf("image detection client is not initialized")
	}
	if len(mergeInfos) == 0 {
		return nil
	}

	// 提取图片URL
	var imageURLs []string
	for _, item := range mergeInfos {
		if item == nil {
			continue // 跳过nil项
		}
		if url, exists := item["Url"]; exists && url != "" {
			imageURLs = append(imageURLs, url)
		}
	}

	if len(imageURLs) == 0 {
		return nil
	}

	// 执行图片检测
	return s.DetectImages(ctx, imageURLs)
}

// DetectImages 检测图片列表
func (s *ImageDetectionService) DetectImages(ctx context.Context, imageURLs []string) error {
	if s == nil {
		return fmt.Errorf("image detection service is nil")
	}
	if s.client == nil {
		return fmt.Errorf("image detection client is not initialized")
	}
	if len(imageURLs) == 0 {
		return nil
	}

	// 创建检测请求
	request := check.NewImageV5CheckRequest(config.GlobConfig.NetEaseYidun.BusinessID)

	// 构建图片信息列表
	var imageBeans []check.ImageBeanRequest
	for i, url := range imageURLs {
		imageBean := check.NewImageBeanRequest()
		imageBean.SetData(url)
		imageBean.SetName(fmt.Sprintf("image_%d", i))
		imageBean.SetType(1) // 1表示URL类型
		imageBeans = append(imageBeans, *imageBean)
	}
	request.SetImages(imageBeans)

	// 发送检测请求
	response, err := s.client.ImageSyncCheck(request)
	if err != nil {
		return fmt.Errorf("send detection request failed: %v", err)
	}

	// 检查检测结果
	return s.validateDetectionResults(response)
}

// validateDetectionResults 验证检测结果
func (s *ImageDetectionService) validateDetectionResults(resp *imagesync.ImageCheckResponse) error {
	if resp == nil {
		return fmt.Errorf("detection response is nil")
	}
	if resp.GetCode() != 200 {
		return fmt.Errorf("detection failed with code: %d, message: %s", resp.GetCode(), resp.GetMsg())
	}

	if resp.Result == nil {
		return fmt.Errorf("detection response result is nil")
	}

	// 检查每个图片的检测结果
	for i, result := range *resp.Result {
		if result.Antispam == nil {
			logger.Logger.Warnf("Image result[%d] antispam is nil, skipping", i)
			continue
		}

		if result.Antispam.Suggestion != nil {
			suggestion := *result.Antispam.Suggestion
			if suggestion != 0 { // 0表示通过，非0表示需要处理
				// 安全地获取DataId，避免nil指针引用
				dataId := "unknown"
				if result.Antispam.DataId != nil {
					dataId = *result.Antispam.DataId
				}
				logger.Logger.Warnf("Image detection failed for dataId: %s, suggestion: %d",
					dataId, suggestion)
				return fmt.Errorf("image detection failed for dataId: %s, suggestion: %d",
					dataId, suggestion)
			}
		}
	}

	logger.Logger.Info("All images passed detection")
	return nil
}
